{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/WebsiteTimE/frontend/src/components/layout/layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/layout.tsx <module evaluation>\",\n    \"Layout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,0VAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2EACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/WebsiteTimE/frontend/src/components/layout/layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/layout.tsx\",\n    \"Layout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,0VAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/WebsiteTimE/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/WebsiteTimE/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,mVAAC;QACC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/WebsiteTimE/frontend/src/app/page.tsx"], "sourcesContent": ["import { Layout } from '@/components/layout/layout';\nimport { Button } from '@/components/ui/button';\nimport { ArrowRight } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function Home() {\n  return (\n    <Layout>\n      <section className=\"py-12 md:py-24 lg:py-32 xl:py-40\">\n        <div className=\"container px-4 md:px-6\">\n          <div className=\"flex flex-col items-center justify-center space-y-6 text-center\">\n            <div className=\"space-y-3\">\n              <h1 className=\"text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl xl:text-7xl\">\n                Qu<PERSON>n lý thời gian hiệu quả\n              </h1>\n              <p className=\"mx-auto max-w-[800px] text-gray-500 md:text-xl dark:text-gray-400\">\n                QLTime giúp bạn sắp xếp công việc, lên lịch hợp lý và đạt đư<PERSON><PERSON> hiệu suất cao nhất trong cuộc sống hàng ngày.\n              </p>\n            </div>\n            <div className=\"space-x-4\">\n              <Link href=\"/tasks\">\n                <Button size=\"lg\" className=\"h-12 px-6 rounded-lg\">\n                  Bắt đầu ngay\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <section className=\"py-12 md:py-24 lg:py-32 backdrop-blur-sm bg-background/40 rounded-xl border border-border/40\">\n        <div className=\"container px-4 md:px-6\">\n          <div className=\"grid gap-10 px-4 sm:px-6 md:gap-16 lg:grid-cols-2\">\n            <div className=\"space-y-4\">\n              <div className=\"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm\">\n                Theo dõi công việc\n              </div>\n              <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">\n                Quản lý danh sách công việc\n              </h2>\n              <p className=\"max-w-[600px] text-gray-500 md:text-xl/relaxed dark:text-gray-400\">\n                Tạo và quản lý các công việc một cách dễ dàng. Thiết lập hạn hoàn thành, ưu tiên và danh mục để tổ chức công việc hiệu quả.\n              </p>\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm\">\n                Lập lịch thông minh\n              </div>\n              <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">\n                Lên lịch và nhắc nhở\n              </h2>\n              <p className=\"max-w-[600px] text-gray-500 md:text-xl/relaxed dark:text-gray-400\">\n                Tạo khung giờ cụ thể cho từng công việc để tối ưu hóa thời gian của bạn. Nhận thông báo nhắc nhở để không bỏ lỡ bất kỳ nhiệm vụ quan trọng nào.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,mVAAC,kJAAA,CAAA,SAAM;;0BACL,mVAAC;gBAAQ,WAAU;0BACjB,cAAA,mVAAC;oBAAI,WAAU;8BACb,cAAA,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAG,WAAU;kDAA0E;;;;;;kDAGxF,mVAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;0CAInF,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC,iQAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,mVAAC,8IAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;4CAAuB;0DAEjD,mVAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,mVAAC;gBAAQ,WAAU;0BACjB,cAAA,mVAAC;oBAAI,WAAU;8BACb,cAAA,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDAA0D;;;;;;kDAGzE,mVAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,mVAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;0CAInF,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDAA0D;;;;;;kDAGzE,mVAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,mVAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/F", "debugId": null}}]}