{"name": "@website-time/frontend", "version": "0.1.0", "description": "Frontend application for WebsiteTimE", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next"}, "dependencies": {"@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/typography": "^0.5.16", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "highlight.js": "^11.11.1", "lucide-react": "^0.483.0", "next": "15.2.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.6.2", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4", "typescript": "^5"}}