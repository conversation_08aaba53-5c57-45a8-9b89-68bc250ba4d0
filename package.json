{"name": "website-time-monorepo", "version": "1.0.0", "description": "WebsiteTimE - Monorepo with pnpm workspace", "private": true, "scripts": {"dev": "pnpm run --parallel dev", "build": "pnpm run --recursive build", "start": "pnpm run --parallel start", "lint": "pnpm run --recursive lint", "test": "pnpm run --recursive test", "clean": "pnpm run --recursive clean", "frontend:dev": "pnpm --filter frontend dev", "frontend:build": "pnpm --filter frontend build", "frontend:start": "pnpm --filter frontend start", "backend:dev": "pnpm --filter backend start:dev", "backend:build": "pnpm --filter backend build", "backend:start": "pnpm --filter backend start:prod", "backend:test": "pnpm --filter backend test", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose up", "docker:down": "docker-compose down"}, "devDependencies": {"@types/node": "^22.10.7", "typescript": "^5.7.3", "prettier": "^3.4.2", "eslint": "^9.18.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}